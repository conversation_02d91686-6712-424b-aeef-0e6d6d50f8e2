{"cells": [{"cell_type": "code", "execution_count": null, "id": "f75733e2", "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "INSTALL = False # Switch this to install dependencies\n", "if INSTALL: # Try installing package with extras\n", "    REPO_URL = \"https://github.com/facebookresearch/dinov2\"\n", "    !{sys.executable} -m pip install -e {REPO_URL}'[extras]' --extra-index-url https://download.pytorch.org/whl/cu117  --extra-index-url https://pypi.nvidia.com\n", "else:\n", "    REPO_PATH = \"<FIXME>\" # Specify a local path to the repository (or use installed package instead)\n", "    sys.path.append(REPO_PATH)"]}, {"cell_type": "markdown", "id": "ff529826", "metadata": {}, "source": ["## <PERSON>ad pretrained dino.txt vision head and text model"]}, {"cell_type": "code", "execution_count": null, "id": "d20749e6", "metadata": {}, "outputs": [], "source": ["from dinov2.hub.dinotxt import dinov2_vitl14_reg4_dinotxt_tet1280d20h24l, get_tokenizer\n", "model = dinov2_vitl14_reg4_dinotxt_tet1280d20h24l().cuda()"]}, {"cell_type": "code", "execution_count": null, "id": "09c04bb1", "metadata": {}, "outputs": [], "source": ["tokenizer = get_tokenizer()"]}, {"cell_type": "markdown", "id": "b85bdd35", "metadata": {}, "source": ["## Load sample image"]}, {"cell_type": "code", "execution_count": null, "id": "0d48a6cf", "metadata": {}, "outputs": [], "source": ["import urllib\n", "from PIL import Image\n", "\n", "def load_image_from_url(url: str) -> Image:\n", "    with urllib.request.urlopen(url) as f:\n", "        return Image.open(f).convert(\"RGB\")\n", "\n", "\n", "EXAMPLE_IMAGE_URL = \"https://dl.fbaipublicfiles.com/dinov2/images/example.jpg\"\n", "img_pil = load_image_from_url(EXAMPLE_IMAGE_URL)\n", "display(img_pil)"]}, {"cell_type": "markdown", "id": "127d7e9d", "metadata": {}, "source": ["## Get Zero-shot classification scores on sample image"]}, {"cell_type": "code", "execution_count": null, "id": "cf2aa337", "metadata": {}, "outputs": [], "source": ["import torch\n", "from dinov2.data.transforms import make_classification_eval_transform\n", "\n", "image_preprocess = make_classification_eval_transform()\n", "image_tensor = torch.stack([image_preprocess(img_pil)], dim=0).cuda()\n", "texts = [\"photo of dogs\", \"photo of a chair\", \"photo of a bowl\", \"photo of a tupperware\"]\n", "class_names = [\"dog\", \"chair\", \"bowl\", \"tupperware\"]\n", "tokenized_texts_tensor = tokenizer.tokenize(texts).cuda()\n", "with torch.autocast('cuda', dtype=torch.float):\n", "    with torch.no_grad():\n", "        image_features = model.encode_image(image_tensor)\n", "        text_features = model.encode_text(tokenized_texts_tensor)\n", "image_features /= image_features.norm(dim=-1, keepdim=True)\n", "text_features /= text_features.norm(dim=-1, keepdim=True)\n", "similarity = (\n", "    text_features.cpu().float().numpy() @ image_features.cpu().float().numpy().T\n", ")\n", "print(similarity) "]}, {"cell_type": "markdown", "id": "c96f5edc", "metadata": {}, "source": ["## Get patch embeddings"]}, {"cell_type": "code", "execution_count": null, "id": "098f7339", "metadata": {}, "outputs": [], "source": ["with torch.autocast('cuda', dtype=torch.float):\n", "    with torch.no_grad():\n", "        image_class_tokens, image_patch_tokens = model.get_visual_class_and_patch_tokens(image_tensor)\n", "        text_features_aligned_to_patch = model.encode_text(tokenized_texts_tensor)[:, 1024:] # Part of text features that is aligned to patch features"]}, {"cell_type": "code", "execution_count": null, "id": "244138fb", "metadata": {}, "outputs": [], "source": ["import torch.nn.functional as F\n", "\n", "B, P, D = image_patch_tokens.shape\n", "H = W = int(P**0.5) \n", "x = image_patch_tokens.movedim(2, 1).unflatten(2, (H, W)).float()  # [B, D, H, W]\n", "x = F.interpolate(x, size=(480, 640), mode=\"bicubic\", align_corners=False)\n", "x = F.normalize(x, p=2, dim=1)\n", "y = F.normalize(text_features_aligned_to_patch.float(), p=2, dim=1)\n", "per_patch_similarity_to_text = torch.einsum(\"bdhw,cd->bchw\", x, y)\n", "pred_idx = per_patch_similarity_to_text.argmax(1).squeeze(0)"]}, {"cell_type": "markdown", "id": "02b6c903", "metadata": {}, "source": ["# Zero-shot classification on ImageNet1k "]}, {"cell_type": "code", "execution_count": null, "id": "ddf76eb0", "metadata": {}, "outputs": [], "source": ["# https://raw.githubusercontent.com/kantharajucn/CLIP-imagenet-evaluation/refs/heads/main/classes.py\n", "imagenet_clip_class_names= [\"tench\", \"goldfish\", \"great white shark\", \"tiger shark\", \"hammerhead shark\", \"electric ray\", \"stingray\", \"rooster\", \"hen\", \"ostrich\", \"brambling\", \"goldfinch\", \"house finch\", \"junco\", \"indigo bunting\", \"American robin\", \"bulbul\", \"jay\", \"magpie\", \"chickadee\", \"American dipper\", \"kite (bird of prey)\", \"bald eagle\", \"vulture\", \"great grey owl\", \"fire salamander\", \"smooth newt\", \"newt\", \"spotted salamander\", \"axolotl\", \"American bullfrog\", \"tree frog\", \"tailed frog\", \"loggerhead sea turtle\", \"leatherback sea turtle\", \"mud turtle\", \"terrapin\", \"box turtle\", \"banded gecko\", \"green iguana\", \"Carolina anole\", \"desert grassland whiptail lizard\", \"agama\", \"frilled-necked lizard\", \"alligator lizard\", \"Gila monster\", \"European green lizard\", \"chameleon\", \"Komodo dragon\", \"Nile crocodile\", \"American alligator\", \"triceratops\", \"worm snake\", \"ring-necked snake\", \"eastern hog-nosed snake\", \"smooth green snake\", \"kingsnake\", \"garter snake\", \"water snake\", \"vine snake\", \"night snake\", \"boa constrictor\", \"African rock python\", \"Indian cobra\", \"green mamba\", \"sea snake\", \"Saharan horned viper\", \"eastern diamondback rattlesnake\", \"sidewinder rattlesnake\", \"trilobite\", \"harvestman\", \"scorpion\", \"yellow garden spider\", \"barn spider\", \"European garden spider\", \"southern black widow\", \"tarantula\", \"wolf spider\", \"tick\", \"centipede\", \"black grouse\", \"ptarmigan\", \"ruffed grouse\", \"prairie grouse\", \"peafowl\", \"quail\", \"partridge\", \"african grey parrot\", \"macaw\", \"sulphur-crested cockatoo\", \"lorikeet\", \"coucal\", \"bee eater\", \"hornbill\", \"hummingbird\", \"jacamar\", \"toucan\", \"duck\", \"red-breasted merganser\", \"goose\", \"black swan\", \"tusker\", \"echidna\", \"platypus\", \"wallaby\", \"koala\", \"wombat\", \"jellyfish\", \"sea anemone\", \"brain coral\", \"flatworm\", \"nematode\", \"conch\", \"snail\", \"slug\", \"sea slug\", \"chiton\", \"chambered nautilus\", \"Dungeness crab\", \"rock crab\", \"fiddler crab\", \"red king crab\", \"American lobster\", \"spiny lobster\", \"crayfish\", \"hermit crab\", \"isopod\", \"white stork\", \"black stork\", \"spoonbill\", \"flamingo\", \"little blue heron\", \"great egret\", \"bittern bird\", \"crane bird\", \"limpkin\", \"common gallinule\", \"American coot\", \"bustard\", \"ruddy turnstone\", \"dunlin\", \"common redshank\", \"dowitcher\", \"oystercatcher\", \"pelican\", \"king penguin\", \"albatross\", \"grey whale\", \"killer whale\", \"dugong\", \"sea lion\", \"Chihuahua\", \"Japanese Chin\", \"Maltese\", \"Pekingese\", \"Shih Tzu\", \"King Charles Spaniel\", \"Papillon\", \"toy terrier\", \"Rhodesian Ridgeback\", \"Afghan Hound\", \"Basset Hound\", \"Beagle\", \"Bloodhound\", \"Bluetick Coonhound\", \"Black and Tan Coonhound\", \"Treeing Walker Coonhound\", \"English foxhound\", \"Redbone Coonhound\", \"borzoi\", \"Irish Wolfhound\", \"Italian Greyhound\", \"Whippet\", \"Ibizan Hound\", \"Norwegian Elkhound\", \"Otterhound\", \"Saluki\", \"Scottish Deerhound\", \"Weimaraner\", \"Staffordshire Bull Terrier\", \"American Staffordshire Terrier\", \"Bedlington Terrier\", \"Border Terrier\", \"Kerry Blue Terrier\", \"Irish Terrier\", \"Norfolk Terrier\", \"Norwich Terrier\", \"Yorkshire Terrier\", \"Wire Fox Terrier\", \"Lakeland Terrier\", \"Sealyham Terrier\", \"Airedale Terrier\", \"Cairn Terrier\", \"Australian Terrier\", \"Dandie Dinmont Terrier\", \"Boston Terrier\", \"Miniature Schnauzer\", \"Giant Schnauzer\", \"Standard Schnauzer\", \"Scottish Terrier\", \"Tibetan Terrier\", \"Australian Silky Terrier\", \"Soft-coated Wheaten Terrier\", \"West Highland White Terrier\", \"Lhasa Apso\", \"Flat-Coated Retriever\", \"Curly-coated Retriever\", \"Golden Retriever\", \"Labrador Retriever\", \"Chesapeake Bay Retriever\", \"German Shorthaired Pointer\", \"Vizsla\", \"English Setter\", \"Irish Setter\", \"Gordon Setter\", \"Brittany dog\", \"Clumber Spaniel\", \"English Springer Spaniel\", \"Welsh Springer Spaniel\", \"Cocker Spaniel\", \"Sussex Spaniel\", \"Irish Water Spaniel\", \"Kuvasz\", \"Schipperke\", \"Groenendael dog\", \"Malinois\", \"Briard\", \"Australian Kelpie\", \"Komondor\", \"Old English Sheepdog\", \"Shetland Sheepdog\", \"collie\", \"Border Collie\", \"Bouvier des Flandres dog\", \"Rottweiler\", \"German Shepherd Dog\", \"Dobermann\", \"Miniature Pinscher\", \"Greater Swiss Mountain Dog\", \"Bernese Mountain Dog\", \"Appenzeller Sennenhund\", \"Entlebucher Sennenhund\", \"Boxer\", \"Bullmastiff\", \"Tibetan Mastiff\", \"French Bulldog\", \"Great Dane\", \"St. Bernard\", \"husky\", \"Alaskan Malamute\", \"Siberian Husky\", \"Dalmatian\", \"Affenpinscher\", \"Basenji\", \"pug\", \"Leonberger\", \"Newfoundland dog\", \"Great Pyrenees dog\", \"Samoyed\", \"Pomeranian\", \"Chow Chow\", \"Keeshond\", \"brussels griffon\", \"Pembroke Welsh Corgi\", \"Cardigan Welsh Corgi\", \"Toy Poodle\", \"Miniature Poodle\", \"Standard Poodle\", \"Mexican hairless dog (xoloitzcuintli)\", \"grey wolf\", \"Alaskan tundra wolf\", \"red wolf or maned wolf\", \"coyote\", \"dingo\", \"dhole\", \"African wild dog\", \"hyena\", \"red fox\", \"kit fox\", \"Arctic fox\", \"grey fox\", \"tabby cat\", \"tiger cat\", \"Persian cat\", \"Siamese cat\", \"Egyptian Mau\", \"cougar\", \"lynx\", \"leopard\", \"snow leopard\", \"jaguar\", \"lion\", \"tiger\", \"cheetah\", \"brown bear\", \"American black bear\", \"polar bear\", \"sloth bear\", \"mongoose\", \"meerkat\", \"tiger beetle\", \"ladybug\", \"ground beetle\", \"longhorn beetle\", \"leaf beetle\", \"dung beetle\", \"rhinoceros beetle\", \"weevil\", \"fly\", \"bee\", \"ant\", \"grasshopper\", \"cricket insect\", \"stick insect\", \"cockroach\", \"praying mantis\", \"cicada\", \"leafhopper\", \"lacewing\", \"dragonfly\", \"damselfly\", \"red admiral butterfly\", \"ringlet butterfly\", \"monarch butterfly\", \"small white butterfly\", \"sulphur butterfly\", \"gossamer-winged butterfly\", \"starfish\", \"sea urchin\", \"sea cucumber\", \"cottontail rabbit\", \"hare\", \"Angora rabbit\", \"hamster\", \"porcupine\", \"fox squirrel\", \"marmot\", \"beaver\", \"guinea pig\", \"common sorrel horse\", \"zebra\", \"pig\", \"wild boar\", \"warthog\", \"hippopotamus\", \"ox\", \"water buffalo\", \"bison\", \"ram (adult male sheep)\", \"bighorn sheep\", \"Alpine ibex\", \"hartebeest\", \"impala (antelope)\", \"gazelle\", \"arabian camel\", \"llama\", \"weasel\", \"mink\", \"European polecat\", \"black-footed ferret\", \"otter\", \"skunk\", \"badger\", \"armadillo\", \"three-toed sloth\", \"orangutan\", \"gorilla\", \"chimpanzee\", \"gibbon\", \"siamang\", \"guenon\", \"patas monkey\", \"baboon\", \"macaque\", \"langur\", \"black-and-white colobus\", \"proboscis monkey\", \"marmoset\", \"white-headed capuchin\", \"howler monkey\", \"titi monkey\", \"Geoffroy's spider monkey\", \"common squirrel monkey\", \"ring-tailed lemur\", \"indri\", \"Asian elephant\", \"African bush elephant\", \"red panda\", \"giant panda\", \"snoek fish\", \"eel\", \"silver salmon\", \"rock beauty fish\", \"clownfish\", \"sturgeon\", \"gar fish\", \"lionfish\", \"pufferfish\", \"abacus\", \"abaya\", \"academic gown\", \"accordion\", \"acoustic guitar\", \"aircraft carrier\", \"airliner\", \"airship\", \"altar\", \"ambulance\", \"amphibious vehicle\", \"analog clock\", \"apiary\", \"apron\", \"trash can\", \"assault rifle\", \"backpack\", \"bakery\", \"balance beam\", \"balloon\", \"ballpoint pen\", \"Band-Aid\", \"banjo\", \"baluster / handrail\", \"barbell\", \"barber chair\", \"barbershop\", \"barn\", \"barometer\", \"barrel\", \"wheelbarrow\", \"baseball\", \"basketball\", \"bassinet\", \"bassoon\", \"swimming cap\", \"bath towel\", \"bathtub\", \"station wagon\", \"lighthouse\", \"beaker\", \"military hat (bearskin or shako)\", \"beer bottle\", \"beer glass\", \"bell tower\", \"baby bib\", \"tandem bicycle\", \"bikini\", \"ring binder\", \"binoculars\", \"birdhouse\", \"boathouse\", \"bobsleigh\", \"bolo tie\", \"poke bonnet\", \"bookcase\", \"bookstore\", \"bottle cap\", \"hunting bow\", \"bow tie\", \"brass memorial plaque\", \"bra\", \"breakwater\", \"breastplate\", \"broom\", \"bucket\", \"buckle\", \"bulletproof vest\", \"high-speed train\", \"butcher shop\", \"taxicab\", \"cauldron\", \"candle\", \"cannon\", \"canoe\", \"can opener\", \"cardigan\", \"car mirror\", \"carousel\", \"tool kit\", \"cardboard box / carton\", \"car wheel\", \"automated teller machine\", \"cassette\", \"cassette player\", \"castle\", \"catamaran\", \"CD player\", \"cello\", \"mobile phone\", \"chain\", \"chain-link fence\", \"chain mail\", \"chainsaw\", \"storage chest\", \"chiffonier\", \"bell or wind chime\", \"china cabinet\", \"Christmas stocking\", \"church\", \"movie theater\", \"cleaver\", \"cliff dwelling\", \"cloak\", \"clogs\", \"cocktail shaker\", \"coffee mug\", \"coffeemaker\", \"spiral or coil\", \"combination lock\", \"computer keyboard\", \"candy store\", \"container ship\", \"convertible\", \"corkscrew\", \"cornet\", \"cowboy boot\", \"cowboy hat\", \"cradle\", \"construction crane\", \"crash helmet\", \"crate\", \"infant bed\", \"Crock Pot\", \"croquet ball\", \"crutch\", \"cuirass\", \"dam\", \"desk\", \"desktop computer\", \"rotary dial telephone\", \"diaper\", \"digital clock\", \"digital watch\", \"dining table\", \"dishcloth\", \"dishwasher\", \"disc brake\", \"dock\", \"dog sled\", \"dome\", \"doormat\", \"drilling rig\", \"drum\", \"drumstick\", \"dumbbell\", \"Dutch oven\", \"electric fan\", \"electric guitar\", \"electric locomotive\", \"entertainment center\", \"envelope\", \"espresso machine\", \"face powder\", \"feather boa\", \"filing cabinet\", \"fireboat\", \"fire truck\", \"fire screen\", \"flagpole\", \"flute\", \"folding chair\", \"football helmet\", \"forklift\", \"fountain\", \"fountain pen\", \"four-poster bed\", \"freight car\", \"French horn\", \"frying pan\", \"fur coat\", \"garbage truck\", \"gas mask or respirator\", \"gas pump\", \"goblet\", \"go-kart\", \"golf ball\", \"golf cart\", \"gondola\", \"gong\", \"gown\", \"grand piano\", \"greenhouse\", \"radiator grille\", \"grocery store\", \"guillotine\", \"hair clip\", \"hair spray\", \"half-track\", \"hammer\", \"hamper\", \"hair dryer\", \"hand-held computer\", \"handkerchief\", \"hard disk drive\", \"harmonica\", \"harp\", \"combine harvester\", \"hatchet\", \"holster\", \"home theater\", \"honeycomb\", \"hook\", \"hoop skirt\", \"gymnastic horizontal bar\", \"horse-drawn vehicle\", \"hourglass\", \"iPod\", \"clothes iron\", \"carved pumpkin\", \"jeans\", \"jeep\", \"T-shirt\", \"jigsaw puzzle\", \"rickshaw\", \"joystick\", \"kimono\", \"knee pad\", \"knot\", \"lab coat\", \"ladle\", \"lampshade\", \"laptop computer\", \"lawn mower\", \"lens cap\", \"letter opener\", \"library\", \"lifeboat\", \"lighter\", \"limousine\", \"ocean liner\", \"lipstick\", \"slip-on shoe\", \"lotion\", \"music speaker\", \"loupe magnifying glass\", \"sawmill\", \"magnetic compass\", \"messenger bag\", \"mailbox\", \"tights\", \"one-piece bathing suit\", \"manhole cover\", \"maraca\", \"marimba\", \"mask\", \"matchstick\", \"maypole\", \"maze\", \"measuring cup\", \"medicine cabinet\", \"megalith\", \"microphone\", \"microwave oven\", \"military uniform\", \"milk can\", \"minibus\", \"miniskirt\", \"minivan\", \"missile\", \"mitten\", \"mixing bowl\", \"mobile home\", \"ford model t\", \"modem\", \"monastery\", \"monitor\", \"moped\", \"mortar and pestle\", \"graduation cap\", \"mosque\", \"mosquito net\", \"vespa\", \"mountain bike\", \"tent\", \"computer mouse\", \"mousetrap\", \"moving van\", \"muzzle\", \"metal nail\", \"neck brace\", \"necklace\", \"baby pacifier\", \"notebook computer\", \"obelisk\", \"oboe\", \"ocarina\", \"odometer\", \"oil filter\", \"pipe organ\", \"oscilloscope\", \"overskirt\", \"bullock cart\", \"oxygen mask\", \"product packet / packaging\", \"paddle\", \"paddle wheel\", \"padlock\", \"paintbrush\", \"pajamas\", \"palace\", \"pan flute\", \"paper towel\", \"parachute\", \"parallel bars\", \"park bench\", \"parking meter\", \"railroad car\", \"patio\", \"payphone\", \"pedestal\", \"pencil case\", \"pencil sharpener\", \"perfume\", \"Petri dish\", \"photocopier\", \"plectrum\", \"Pickelhaube\", \"picket fence\", \"pickup truck\", \"pier\", \"piggy bank\", \"pill bottle\", \"pillow\", \"ping-pong ball\", \"pinwheel\", \"pirate ship\", \"drink pitcher\", \"block plane\", \"planetarium\", \"plastic bag\", \"plate rack\", \"farm plow\", \"plunger\", \"Polaroid camera\", \"pole\", \"police van\", \"poncho\", \"pool table\", \"soda bottle\", \"plant pot\", \"potter's wheel\", \"power drill\", \"prayer rug\", \"printer\", \"prison\", \"missile\", \"projector\", \"hockey puck\", \"punching bag\", \"purse\", \"quill\", \"quilt\", \"race car\", \"racket\", \"radiator\", \"radio\", \"radio telescope\", \"rain barrel\", \"recreational vehicle\", \"fishing casting reel\", \"reflex camera\", \"refrigerator\", \"remote control\", \"restaurant\", \"revolver\", \"rifle\", \"rocking chair\", \"rotisserie\", \"eraser\", \"rugby ball\", \"ruler measuring stick\", \"sneaker\", \"safe\", \"safety pin\", \"salt shaker\", \"sandal\", \"sarong\", \"saxophone\", \"scabbard\", \"weighing scale\", \"school bus\", \"schooner\", \"scoreboard\", \"CRT monitor\", \"screw\", \"screwdriver\", \"seat belt\", \"sewing machine\", \"shield\", \"shoe store\", \"shoji screen / room divider\", \"shopping basket\", \"shopping cart\", \"shovel\", \"shower cap\", \"shower curtain\", \"ski\", \"balaclava ski mask\", \"sleeping bag\", \"slide rule\", \"sliding door\", \"slot machine\", \"snorkel\", \"snowmobile\", \"snowplow\", \"soap dispenser\", \"soccer ball\", \"sock\", \"solar thermal collector\", \"sombrero\", \"soup bowl\", \"keyboard space bar\", \"space heater\", \"space shuttle\", \"spatula\", \"motorboat\", \"spider web\", \"spindle\", \"sports car\", \"spotlight\", \"stage\", \"steam locomotive\", \"through arch bridge\", \"steel drum\", \"stethoscope\", \"scarf\", \"stone wall\", \"stopwatch\", \"stove\", \"strainer\", \"tram\", \"stretcher\", \"couch\", \"stupa\", \"submarine\", \"suit\", \"sundial\", \"sunglasses\", \"sunglasses\", \"sunscreen\", \"suspension bridge\", \"mop\", \"sweatshirt\", \"swim trunks / shorts\", \"swing\", \"electrical switch\", \"syringe\", \"table lamp\", \"tank\", \"tape player\", \"teapot\", \"teddy bear\", \"television\", \"tennis ball\", \"thatched roof\", \"front curtain\", \"thimble\", \"threshing machine\", \"throne\", \"tile roof\", \"toaster\", \"tobacco shop\", \"toilet seat\", \"torch\", \"totem pole\", \"tow truck\", \"toy store\", \"tractor\", \"semi-trailer truck\", \"tray\", \"trench coat\", \"tricycle\", \"trimaran\", \"tripod\", \"triumphal arch\", \"trolleybus\", \"trombone\", \"hot tub\", \"turnstile\", \"typewriter keyboard\", \"umbrella\", \"unicycle\", \"upright piano\", \"vacuum cleaner\", \"vase\", \"vaulted or arched ceiling\", \"velvet fabric\", \"vending machine\", \"vestment\", \"viaduct\", \"violin\", \"volleyball\", \"waffle iron\", \"wall clock\", \"wallet\", \"wardrobe\", \"military aircraft\", \"sink\", \"washing machine\", \"water bottle\", \"water jug\", \"water tower\", \"whiskey jug\", \"whistle\", \"hair wig\", \"window screen\", \"window shade\", \"Windsor tie\", \"wine bottle\", \"airplane wing\", \"wok\", \"wooden spoon\", \"wool\", \"split-rail fence\", \"shipwreck\", \"sailboat\", \"yurt\", \"website\", \"comic book\", \"crossword\", \"traffic or street sign\", \"traffic light\", \"dust jacket\", \"menu\", \"plate\", \"guacamole\", \"consomme\", \"hot pot\", \"trifle\", \"ice cream\", \"popsicle\", \"baguette\", \"bagel\", \"pretzel\", \"cheeseburger\", \"hot dog\", \"mashed potatoes\", \"cabbage\", \"broccoli\", \"cauliflower\", \"zucchini\", \"spaghetti squash\", \"acorn squash\", \"butternut squash\", \"cucumber\", \"artichoke\", \"bell pepper\", \"cardoon\", \"mushroom\", \"Granny Smith apple\", \"strawberry\", \"orange\", \"lemon\", \"fig\", \"pineapple\", \"banana\", \"jackfruit\", \"cherimoya (custard apple)\", \"pomegranate\", \"hay\", \"carbonara\", \"chocolate syrup\", \"dough\", \"meatloaf\", \"pizza\", \"pot pie\", \"burrito\", \"red wine\", \"espresso\", \"tea cup\", \"eggnog\", \"mountain\", \"bubble\", \"cliff\", \"coral reef\", \"geyser\", \"lakeshore\", \"promontory\", \"sandbar\", \"beach\", \"valley\", \"volcano\", \"baseball player\", \"bridegroom\", \"scuba diver\", \"rapeseed\", \"daisy\", \"yellow lady's slipper\", \"corn\", \"acorn\", \"rose hip\", \"horse chestnut seed\", \"coral fungus\", \"agaric\", \"gyromitra\", \"stinkhorn mushroom\", \"earth star fungus\", \"hen of the woods mushroom\", \"bolete\", \"corn cob\", \"toilet paper\"]\n"]}, {"cell_type": "code", "execution_count": null, "id": "7e077342", "metadata": {}, "outputs": [], "source": ["# The following comes from here: https://github.com/mlfoundations/open_clip/blob/main/src/open_clip/zero_shot_metadata.py\n", "# Original reference: https://github.com/openai/CLIP/blob/main/notebooks/Prompt_Engineering_for_ImageNet.ipynb\n", "openai_imagenet_templates = (\n", "    lambda c: f\"a bad photo of a {c}.\",\n", "    lambda c: f\"a photo of many {c}.\",\n", "    lambda c: f\"a sculpture of a {c}.\",\n", "    lambda c: f\"a photo of the hard to see {c}.\",\n", "    lambda c: f\"a low resolution photo of the {c}.\",\n", "    lambda c: f\"a rendering of a {c}.\",\n", "    lambda c: f\"graffiti of a {c}.\",\n", "    lambda c: f\"a bad photo of the {c}.\",\n", "    lambda c: f\"a cropped photo of the {c}.\",\n", "    lambda c: f\"a tattoo of a {c}.\",\n", "    lambda c: f\"the embroidered {c}.\",\n", "    lambda c: f\"a photo of a hard to see {c}.\",\n", "    lambda c: f\"a bright photo of a {c}.\",\n", "    lambda c: f\"a photo of a clean {c}.\",\n", "    lambda c: f\"a photo of a dirty {c}.\",\n", "    lambda c: f\"a dark photo of the {c}.\",\n", "    lambda c: f\"a drawing of a {c}.\",\n", "    lambda c: f\"a photo of my {c}.\",\n", "    lambda c: f\"the plastic {c}.\",\n", "    lambda c: f\"a photo of the cool {c}.\",\n", "    lambda c: f\"a close-up photo of a {c}.\",\n", "    lambda c: f\"a black and white photo of the {c}.\",\n", "    lambda c: f\"a painting of the {c}.\",\n", "    lambda c: f\"a painting of a {c}.\",\n", "    lambda c: f\"a pixelated photo of the {c}.\",\n", "    lambda c: f\"a sculpture of the {c}.\",\n", "    lambda c: f\"a bright photo of the {c}.\",\n", "    lambda c: f\"a cropped photo of a {c}.\",\n", "    lambda c: f\"a plastic {c}.\",\n", "    lambda c: f\"a photo of the dirty {c}.\",\n", "    lambda c: f\"a jpeg corrupted photo of a {c}.\",\n", "    lambda c: f\"a blurry photo of the {c}.\",\n", "    lambda c: f\"a photo of the {c}.\",\n", "    lambda c: f\"a good photo of the {c}.\",\n", "    lambda c: f\"a rendering of the {c}.\",\n", "    lambda c: f\"a {c} in a video game.\",\n", "    lambda c: f\"a photo of one {c}.\",\n", "    lambda c: f\"a doodle of a {c}.\",\n", "    lambda c: f\"a close-up photo of the {c}.\",\n", "    lambda c: f\"a photo of a {c}.\",\n", "    lambda c: f\"the origami {c}.\",\n", "    lambda c: f\"the {c} in a video game.\",\n", "    lambda c: f\"a sketch of a {c}.\",\n", "    lambda c: f\"a doodle of the {c}.\",\n", "    lambda c: f\"a origami {c}.\",\n", "    lambda c: f\"a low resolution photo of a {c}.\",\n", "    lambda c: f\"the toy {c}.\",\n", "    lambda c: f\"a rendition of the {c}.\",\n", "    lambda c: f\"a photo of the clean {c}.\",\n", "    lambda c: f\"a photo of a large {c}.\",\n", "    lambda c: f\"a rendition of a {c}.\",\n", "    lambda c: f\"a photo of a nice {c}.\",\n", "    lambda c: f\"a photo of a weird {c}.\",\n", "    lambda c: f\"a blurry photo of a {c}.\",\n", "    lambda c: f\"a cartoon {c}.\",\n", "    lambda c: f\"art of a {c}.\",\n", "    lambda c: f\"a sketch of the {c}.\",\n", "    lambda c: f\"a embroidered {c}.\",\n", "    lambda c: f\"a pixelated photo of a {c}.\",\n", "    lambda c: f\"itap of the {c}.\",\n", "    lambda c: f\"a jpeg corrupted photo of the {c}.\",\n", "    lambda c: f\"a good photo of a {c}.\",\n", "    lambda c: f\"a plushie {c}.\",\n", "    lambda c: f\"a photo of the nice {c}.\",\n", "    lambda c: f\"a photo of the small {c}.\",\n", "    lambda c: f\"a photo of the weird {c}.\",\n", "    lambda c: f\"the cartoon {c}.\",\n", "    lambda c: f\"art of the {c}.\",\n", "    lambda c: f\"a drawing of the {c}.\",\n", "    lambda c: f\"a photo of the large {c}.\",\n", "    lambda c: f\"a black and white photo of a {c}.\",\n", "    lambda c: f\"the plushie {c}.\",\n", "    lambda c: f\"a dark photo of a {c}.\",\n", "    lambda c: f\"itap of a {c}.\",\n", "    lambda c: f\"graffiti of the {c}.\",\n", "    lambda c: f\"a toy {c}.\",\n", "    lambda c: f\"itap of my {c}.\",\n", "    lambda c: f\"a photo of a cool {c}.\",\n", "    lambda c: f\"a photo of a small {c}.\",\n", "    lambda c: f\"a tattoo of the {c}.\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c080a969", "metadata": {}, "outputs": [], "source": ["from torchvision.datasets import ImageFolder\n", "# Please update the following directory to the root of ImageNet1k val dataset.\n", "imagenet_val_root_dir = \"<FIXME>\"\n", "val_dataset = ImageFolder(imagenet_val_root_dir, image_preprocess)"]}, {"cell_type": "code", "execution_count": null, "id": "0cc1d9d9", "metadata": {}, "outputs": [], "source": ["def zeroshot_classifier(classnames, templates, tokenizer):\n", "    with torch.no_grad():\n", "        zeroshot_weights = []\n", "        for classname in classnames:\n", "            texts = [template(classname) for template in templates] #format with class\n", "            texts = tokenizer.tokenize(texts).cuda() #tokenize\n", "            class_embeddings = model.encode_text(texts) #embed with text encoder\n", "            class_embeddings /= class_embeddings.norm(dim=-1, keepdim=True)\n", "            class_embedding = class_embeddings.mean(dim=0)\n", "            class_embedding /= class_embedding.norm()\n", "            zeroshot_weights.append(class_embedding)\n", "        zeroshot_weights = torch.stack(zeroshot_weights, dim=1).cuda()\n", "    return zeroshot_weights\n", "zeroshot_weights = zeroshot_classifier(imagenet_clip_class_names, openai_imagenet_templates, tokenizer)"]}, {"cell_type": "code", "execution_count": null, "id": "e7e3523e", "metadata": {}, "outputs": [], "source": ["class_name_to_ids = {class_name:idx for idx, class_name in enumerate(imagenet_clip_class_names)}"]}, {"cell_type": "code", "execution_count": null, "id": "eef6c047", "metadata": {}, "outputs": [], "source": ["def accuracy(output, target, topk=(1,)):\n", "    pred = output.topk(max(topk), 1, True, True)[1].t()\n", "    correct = pred.eq(target.view(1, -1).expand_as(pred))\n", "    return [correct[:k].reshape(-1).sum(0, keepdim=True) for k in topk]"]}, {"cell_type": "code", "execution_count": null, "id": "6d2a9ff8", "metadata": {}, "outputs": [], "source": ["from tqdm.notebook import tqdm\n", "\n", "images = []\n", "class_ids = []\n", "batch_size = 64\n", "num_workers = 8\n", "top1, top5, n = 0., 0., 0.\n", "val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=num_workers, pin_memory=True)\n", "for images, targets in tqdm(val_loader):\n", "    with torch.autocast('cuda', dtype=torch.float):\n", "        with torch.no_grad():\n", "            image_features = model.encode_image(images.cuda())\n", "            image_features /= image_features.norm(dim=-1, keepdim=True)\n", "            logits = 100. * image_features @ zeroshot_weights\n", "            acc1, acc5 = accuracy(logits, targets.cuda(), topk=(1, 5))\n", "            top1 += acc1\n", "            top5 += acc5\n", "            n += len(images)\n", "    images = []\n", "    class_ids = []\n", "top1 = (top1.item() / n) * 100\n", "top5 = (top5.item() / n) * 100 \n", "print(f\"Top-1 accuracy: {top1}\")\n", "print(f\"Top-5 accuracy: {top5}\")"]}], "metadata": {"kernelspec": {"display_name": "fairvit-py311-ptnightly-xformers-20250129", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}