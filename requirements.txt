absl-py==1.4.0
addict==2.4.0
aiofiles==23.2.1
aiohttp==3.8.5
aiosignal==1.3.1
altair==5.1.0
annotated-types==0.5.0
ansi2html==1.8.0
antlr4-python3-runtime==4.9.3
anyio==3.7.1
asttokens==2.4.1
async-timeout==4.0.3
attrs==23.1.0
blinker==1.6.2
boto3==1.28.62
botocore==1.31.62
bottle==0.12.25
Brotli
cachetools==5.3.1
cattrs==23.1.2
certifi 
cffi 
chainmap==1.0.3
chardet==5.2.0
charset-normalizer 
click==8.1.7
cloudpickle==2.2.1
cmake==3.27.0
colorama 
combomethod==1.0.12
comm==0.1.4
common==0.1.2
ConfigArgParse==1.7
contourpy==1.1.0
cryptography 
cycler==0.11.0
Cython==3.0.5
dash==2.14.1
dash-core-components==2.0.0
dash-html-components==2.0.0
dash-table==5.0.0
data==0.4
debugpy==1.8.1
decorator==5.1.1
dual==0.0.10
dynamo3==0.4.10
easydict==1.10
efficientnet-pytorch==0.7.1
einops==0.3.2
exceptiongroup==1.1.3
executing==2.0.1
fastapi==0.103.0
fastjsonschema==2.18.1
ffmpy==0.3.1
filelock==3.12.2
Flask==2.3.3
Flask-Cors==4.0.0
flywheel==0.5.4
fonttools==4.42.0
frozenlist==1.4.0
fsspec==2023.6.0
funcsigs==1.0.2
future 
fvcore 
google-auth==2.22.0
google-auth-oauthlib==1.0.0
gradio
gradio_client
grpcio==1.57.0
h11==0.14.0
h5py==3.9.0
httpcore==0.17.3
httpx==0.24.1
huggingface-hub==0.16.4
idna 
imageio==2.31.1
importlib-metadata==6.8.0
importlib-resources==6.0.1
iopath==0.1.9
ipython==8.17.2
ipywidgets==8.1.1
itsdangerous==2.1.2
jedi==0.19.1
Jinja2==3.1.2
jmespath==1.0.1
joblib==1.3.2
jsonschema==4.19.0
jsonschema-specifications==2023.7.1
jupyter_core==5.5.0
jupyterlab-widgets==3.0.9
kiwisolver==1.4.4
kornia==0.7.0
lazy_loader==0.3
lightning-utilities==0.9.0
lit==16.0.6
Markdown==3.4.4
markdown-it-py==3.0.0
MarkupSafe==2.1.3
matplotlib==3.3.4
matplotlib-inline==0.1.6
mdurl==0.1.2
mpmath==1.3.0
multidict==6.0.4
mypy-extensions==1.0.0
natsort==8.4.0
nbformat==5.7.0
ndim
nest-asyncio==1.5.8
networkx==3.1
ntplib==0.4.0
nulltype==2.3.1
numpy 
nvidia-cublas-cu11==**********
nvidia-cuda-cupti-cu11==11.7.101
nvidia-cuda-nvrtc-cu11==11.7.99
nvidia-cuda-runtime-cu11==11.7.99
nvidia-cudnn-cu11==********
nvidia-cufft-cu11==*********
nvidia-curand-cu11==**********
nvidia-cusolver-cu11==********
nvidia-cusparse-cu11==*********
nvidia-nccl-cu11==2.14.3
nvidia-nvtx-cu11==11.7.91
oauthlib==3.2.2
omegaconf==2.3.0
open3d==0.17.0
opencv-python==********
options==1.4.10
orjson==3.9.5
packaging==23.1

pandas==2.0.3
parso==0.8.3
peewee==3.16.3
pexpect==4.8.0
Pillow==10.0.0
platformdirs==3.10.0
plotly==5.18.0
portalocker 
progressbar2==4.2.0
prompt-toolkit==3.0.39
protobuf==4.24.2
prox==0.0.17
ptflops==*******
ptyprocess==0.7.0
pure-eval==0.2.2
py-machineid==0.4.3
pyasn1==0.5.0
pyasn1-modules==0.3.0
pybind11==2.11.1
pycparser 
pydantic==2.3.0
pydantic_core==2.6.3

pyDeprecate==0.3.2
pydub==0.25.1
Pygments==2.16.1
PyNaCl==1.5.0
pyOpenSSL 
pyparsing==3.0.9
pyquaternion==0.9.9
pyre-extensions==0.0.29
PySocks 
python-dateutil==2.8.2
python-geoip-python3==1.3

python-multipart==0.0.6
python-utils==3.7.0

pytz==2023.3
PyWavelets==1.4.1
PyYAML==6.0.1
referencing==0.30.2
requests 
requests-cache
requests-oauthlib==1.3.1
retrying==1.3.4
rich==13.5.2
rich-argparse==1.3.0
rpds-py==0.10.0
rsa==4.9
s3transfer==0.7.0
safetensors==0.3.1
scikit-image==0.21.0
scikit-learn==1.3.0
scipy==1.11.1
seaborn==0.12.2
semantic-version==2.10.0
six==1.12.0

sniffio==1.3.0
stack-data==0.6.3
starlette==0.27.0
sympy==1.12
tabulate 
tenacity==8.2.3
tensorboard==2.14.0
tensorboard-data-server==0.7.1

termcolor 
thop==0.1.1.post2209072238
threadpoolctl==3.2.0
tifffile==2023.7.18
tight==0.1.0
timm==0.9.5
tomli==2.0.1
tomli_w==1.0.0
toolz==0.12.0
gdown

torchmetrics==1.1.1
torchstat==0.0.7
torchsummary==1.5.1

tqdm==4.65.0
traitlets==5.13.0
triton==2.0.0
typing-inspect==0.9.0
typing_extensions 
tzdata==2023.3
url-normalize==1.4.3
urllib3==1.26.16
uvicorn==0.23.2
wcwidth==0.2.9
websockets==11.0.3
Werkzeug==2.3.7
widgetsnbextension==4.0.9
wrapt==1.15.0
x21
xformers==0.0.20
yacs 
yarl==1.9.2
zipp==3.16.2
